import * as vscode from "vscode";
import { AIResourcesViewProvider } from "./providers/aiResourcesViewProvider";
import { MarkdownProvider } from "./providers/markdownProvider";

interface ResourceProvider {
	refresh(): void;
}
import { SettingsWebviewPanel } from "./panels/settingsWebviewPanel";
import { setLoginContext } from "./utils/context";
import {
	clearAllGlobalState,
	initGlobalState,
	setPtKey,
	getCurrentApiKey,
	setCurrentApiKey,
} from "./config";
import { JoycodeHelper } from "./utils/joycodeHelper";
import { Logger } from "./utils/logger";

// 顶部添加全局变量
let currentMarkdownContent = "";
let providerRegistered = false;

interface UserInfo {
	pt_key?: string;
}

// 初始化用户会话的辅助函数
async function initializeUserSession(settingsPanel: SettingsWebviewPanel, currentProvider: ResourceProvider) {
	try {
		// 用户已登录，获取用户信息
		const userInfo = await vscode.commands.executeCommand("workbench.action.joycoderGetLoginInfo") as UserInfo;
		Logger.info("用户已登录，开始初始化：" + JSON.stringify(userInfo));

		if (userInfo?.pt_key) {
			setPtKey(userInfo.pt_key);
			setLoginContext(true);

			// 尝试获取并设置 API key，但不影响列表展示
			try {
				const keyList = await settingsPanel.fetchAndMaskKeyList();
				if (keyList && keyList.length > 0) {
					await setCurrentApiKey(String(keyList[0].value));
					Logger.info("成功获取并设置 API key");
				} else {
					Logger.info("没有可用的 API key");
				}
			} catch (error) {
				Logger.warn("获取 API key 失败：" + error);
			}

			// 检查并写入apikey
			const apiKeyFileUri = JoycodeHelper.getApiKeyFileUri();
			if (apiKeyFileUri && !(await JoycodeHelper.exists(apiKeyFileUri))) {
				try {
					const keyList = await settingsPanel.fetchAndMaskKeyList();
					if (keyList && keyList.length > 0) {
						await JoycodeHelper.ensureApiKeyFile(String(keyList[0].value));
						Logger.info("成功获取并写入API key");
					} else {
						Logger.info("没有可用的API key，跳过文件创建");
					}
				} catch (error) {
					Logger.warn("获取API key失败，跳过文件创建: " + error);
				}
			} else {
				Logger.info("API key文件已存在，跳过处理");
			}

			// 显示主界面
			vscode.commands.executeCommand("workbench.view.extension.ai-resources");
			currentProvider.refresh();
		} else {
			Logger.warn("获取用户信息失败");
			clearAllGlobalState();
			setLoginContext(false);
			currentProvider.refresh();
		}
	} catch (error) {
		Logger.error("初始化用户会话失败：" + error);
		clearAllGlobalState();
		setLoginContext(false);
		currentProvider.refresh();
	}
}

export async function activate(context: vscode.ExtensionContext) {
	const isRemote = !!vscode.env.remoteName;
	const isJoyCode = vscode.env.uriScheme === "joycoder";
	Logger.info(`扩展激活。当前环境: ${isRemote ? "远程" : "本地"}`);
	Logger.info("uriScheme：" + vscode.env.uriScheme);

	// 初始化全局状态
	initGlobalState(context);

	const mdProvider = new MarkdownProvider();
	context.subscriptions.push(
		vscode.workspace.registerTextDocumentContentProvider(
			"plugin-detail",
			mdProvider,
		),
	);
	// 创建WebView实现的provider
	const webviewProvider = new AIResourcesViewProvider(context, mdProvider);

	// 使用WebView实现
	let currentProvider: ResourceProvider = webviewProvider;
	const settingsPanel = new SettingsWebviewPanel(context);

	// 首先注册WebView provider - 无论登录状态如何都要注册
	const webviewProviderDisposable = vscode.window.registerWebviewViewProvider(
		"aiResourcesView",
		webviewProvider
	);
	context.subscriptions.push(webviewProviderDisposable);

	// 监听IDE登录状态变化
	context.subscriptions.push(
		vscode.commands.registerCommand("workbench.action.joycoderLoginStatusChanged", async (isLoggedIn: boolean) => {
			Logger.info(`IDE登录状态变化: ${isLoggedIn}`);
			if (!isLoggedIn) {
				clearAllGlobalState();
				setLoginContext(false);
				currentProvider.refresh();
			}
		})
	);

	const callbackId = `joycoder.callback.resource`;

	// 如果不是JoyCode环境，显示错误信息但仍然注册provider以显示错误UI
	if (!isJoyCode) {
		Logger.error("非法情况！请在joyCode软件中使用该插件！");
		vscode.window.showInformationMessage("请在joyCode软件中使用该插件！");
		clearAllGlobalState();
		setLoginContext(false);
		currentProvider.refresh();
		// 不要return，继续注册其他命令
	} else {
		// 只有在JoyCode环境下才检查登录状态
		try {
			const isLoggedIn = await vscode.commands.executeCommand("workbench.action.joycoderIsLoggedIn", callbackId);
			if (!isLoggedIn) {
				Logger.warn("用户未登录，显示登录界面");
				clearAllGlobalState();
				setLoginContext(false);
				currentProvider.refresh();
			} else {
				// 用户已登录，初始化用户信息
				await initializeUserSession(settingsPanel, currentProvider);
			}
		} catch (error) {
			Logger.error("检查登录状态失败：" + error);
			clearAllGlobalState();
			setLoginContext(false);
			currentProvider.refresh();
		}
	}

	// 这部分逻辑已经移到 initializeUserSession 函数中
	const loginStatusChangedCommand = vscode.commands.registerCommand(
		`${callbackId}`,
		async (params: any) => {
			Logger.info("ide登录态发生了变化：" + JSON.stringify(params));
			if (params?.userInfo?.pt_key) {
				Logger.info("用户登录了！");
				setPtKey(params?.userInfo?.pt_key);
				setLoginContext(true);

				// 尝试获取并设置 API key，但不影响列表展示
				try {
					const keyList = await settingsPanel.fetchAndMaskKeyList();
					if (keyList && keyList.length > 0) {
						await setCurrentApiKey(String(keyList[0].value));
						Logger.info("成功获取并设置 API key");
					} else {
						Logger.info("没有可用的 API key");
					}
				} catch (error) {
					Logger.warn("获取 API key 失败：" + error);
				}

				currentProvider.refresh();
			} else {
				Logger.warn("用户退出了！");
				await clearAllGlobalState();
				await setCurrentApiKey("");
				setLoginContext(false);
				currentProvider.refresh();
			}
		},
	);
	context.subscriptions.push(loginStatusChangedCommand);

	// showMainView 函数已经不需要了，直接在 initializeUserSession 中调用

	// 登录状态检查已经在前面处理过了，这里不需要重复处理

	// WebView provider 已经在前面注册过了


	// 注册资源选择命令
	context.subscriptions.push(
		vscode.commands.registerCommand('aiResources.selectResource', (item) => {
			if (item) {
				vscode.commands.executeCommand('aiResources.showDetail', item.id);
			}
		})
	);

	// 注册资源加载命令
	context.subscriptions.push(
		vscode.commands.registerCommand('aiResources.fetchResources', async (_params) => {
			// TODO: 实现实际的资源获取逻辑
			return {
				records: [
					{
						pluginId: '1',
						name: '示例资源1',
						pluginDesc: '这是一个示例资源',
						iconUrl: 'https://files.codelife.cc/website/github.svg'
					},
					{
						pluginId: '2',
						name: '示例资源2',
						pluginDesc: '这是另一个示例资源',
						iconUrl: 'https://files.codelife.cc/website/github.svg'
					}
				]
			};
		})
	);
	// webviewProviderDisposable 已经在前面添加到 subscriptions 中了

	context.subscriptions.push(
		vscode.commands.registerCommand("aiResources.openSettings", () => {
			settingsPanel.show();
		}),
		// 注册写入apikey的命令
		vscode.commands.registerCommand("aiResources.writeApiKey", async () => {
			try {
				const keyList = await settingsPanel.fetchAndMaskKeyList();
				if (keyList && keyList.length > 0) {
					await JoycodeHelper.ensureApiKeyFile(String(keyList[0].value));
					vscode.window.showInformationMessage("API key写入成功");
				} else {
					vscode.window.showWarningMessage("没有可用的API key");
				}
			} catch (error) {
				vscode.window.showErrorMessage("API key写入失败: " + error);
			}
		}),
		vscode.commands.registerCommand(
			"aiResources.showDetail",
			(resourceId: string) => {
				// 检查是否有 API key
				if (!getCurrentApiKey()) {
					vscode.window.showWarningMessage("请先设置 API key", "打开设置").then(selection => {
						if (selection === "打开设置") {
							vscode.commands.executeCommand("aiResources.openSettings");
						}
					});
					return;
				}

				const mdContent = `# 插件详情\n\n暂无详情信息`;
				const uri = vscode.Uri.parse(`airesource-detail:${resourceId}.md`);
				const provider = new (class
					implements vscode.TextDocumentContentProvider {
					provideTextDocumentContent() {
						return mdContent;
					}
				})();
				const reg = vscode.workspace.registerTextDocumentContentProvider(
					"airesource-detail",
					provider,
				);
				context.subscriptions.push(reg);
				vscode.commands.executeCommand("markdown.showPreview", uri);
			},
		),
	);
}

export function deactivate() { }

