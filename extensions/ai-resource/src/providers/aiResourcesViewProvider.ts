import * as vscode from "vscode";
import { getListPage, getPluginInfo } from "../service/api";
import { getCurrentApiKey, setCurrentApiKey } from "../config";
import { MarkdownProvider } from "./markdownProvider";
// import * as os from "os";
import * as path from "path";
import * as fs from "fs";
import { Logger } from "../utils/logger";
import { JoycodeHelper } from "../utils/joycodeHelper";

export class AIResourcesViewProvider implements vscode.WebviewViewProvider {
	private webviewView?: vscode.WebviewView;
	private context: vscode.ExtensionContext;
	private mdProvider: MarkdownProvider;
	private isInitializing: boolean = false;

	constructor(context: vscode.ExtensionContext, mdProvider: MarkdownProvider) {
		this.context = context;
		this.mdProvider = mdProvider;
	}

	private async fetchListData(query = "", pageNum = 1, pageSize = 25) {
		try {
			const res = await getListPage({ pageNum, pageSize, query });
			const data = res.data?.data || res.data;
			if (data && Array.isArray(data.records)) {
				return data.records;
			}
			return [];
		} catch (e) {
			return [];
		}
	}

	private async isLoggedIn(): Promise<boolean> {
		const callbackId = `joycoder.callback.resource`;
		return await vscode.commands.executeCommand("workbench.action.joycoderIsLoggedIn", callbackId) as boolean;
	}

	public setInitializing(initializing: boolean) {
		this.isInitializing = initializing;
		Logger.info(`[AIResourcesViewProvider] 设置初始化状态: ${initializing}`);
	}

	public async refresh() {
		if (this.webviewView) {
			const currentKey = getCurrentApiKey();
			const isLoggedIn = await this.isLoggedIn();
			Logger.info(
				`[AIResourcesViewProvider] refresh调用 - 当前API key存在: ${!!currentKey}, 登录状态: ${isLoggedIn}`,
			);

			this.webviewView.webview.html = await this.getWebviewContent();
			if (isLoggedIn) {
				const records = await this.fetchListData();
				this.webviewView?.webview.postMessage({ type: "listData", records });
			}
		}
	}

	async resolveWebviewView(webviewView: vscode.WebviewView): Promise<void> {
		this.webviewView = webviewView;
		webviewView.webview.options = {
			enableScripts: true,
		};

		// 每次面板显示时，优先同步本地 key 到工作区状态
		// 对于新工作区，优先从文件读取；如果文件读取失败，在远程环境下不清空（可能在extension.ts中已设置）
		const currentKey = getCurrentApiKey();
		const isRemote = !!vscode.env.remoteName;
		const workspaceName = vscode.workspace.name || "unknown";

		Logger.info(
			`[AIResourcesViewProvider] resolveWebviewView - 工作区: ${workspaceName}, 初始化状态: ${this.isInitializing}, 远程环境: ${isRemote}, 当前API key存在: ${!!currentKey}`,
		);

		// 如果当前工作区还没有API key，尝试从文件读取
		if (!currentKey) {
			try {
				const apikey = await JoycodeHelper.readApiKey();
				if (typeof apikey === "string" && apikey.trim()) {
					await setCurrentApiKey(apikey);
					Logger.info(
						"[AIResourcesViewProvider] 成功从本地文件读取API key到新工作区",
					);
				} else {
					Logger.info(
						"[AIResourcesViewProvider] 文件中无有效API key，新工作区保持空状态",
					);
					// 不主动清空，让extension.ts中的逻辑处理
				}
			} catch (error) {
				Logger.error("[AIResourcesViewProvider] 读取API key失败: " + error);
				// 不主动清空，让extension.ts中的逻辑处理
			}
		} else {
			Logger.info("[AIResourcesViewProvider] 当前工作区已有API key，保持不变");
		}

		webviewView.webview.html = await this.getWebviewContent();

		const isLoggedIn = await this.isLoggedIn();
		if (isLoggedIn) {
			const records = await this.fetchListData();
			webviewView.webview.postMessage({ type: "listData", records });
		}

		webviewView.webview.onDidReceiveMessage(async (message) => {
			const isLoggedIn = await this.isLoggedIn();
			if (!isLoggedIn) {
				if (message.type === "login") {
					vscode.commands.executeCommand("workbench.action.joycoderLogin");
				}
				return;
			}
			switch (message.type) {
				case "select": {
					try {
						const res = await getPluginInfo(message.id);
						const data = res?.data?.data;
						let mdContent = "";
						if (data) {
							mdContent = `# ${data.name} 服务接口API文档\n\n`;
							if (data.tools && data.tools.length > 0) {
								data.tools.forEach((tool: any, index: number) => {
									const nestedStructures: any[] = [];
									const collectNestedStructures = (
										paramName: string,
										children: any[],
										level: number = 0,
									) => {
										nestedStructures.push({
											name: paramName,
											children: children,
											level: level,
										});
										children.forEach((child: any) => {
											if (child.children && child.children.length > 0) {
												collectNestedStructures(
													child.paramName,
													child.children,
													level + 1,
												);
											}
										});
									};
									mdContent += `## 服务名称\n\n${tool.name}\n\n`;
									mdContent += `## 服务描述\n\n${tool.toolDesc || ""}\n\n`;
									mdContent += `## 接口地址\n\n\`${tool.requestMethod.toUpperCase()} ${tool.toolUrl}\`\n\n`;
									if (tool.requestParams && tool.requestParams.length > 0) {
										mdContent += `## 接口入参\n\n`;
										mdContent += `| 参数名 | 类型 | 必填 | 说明 |\n`;
										mdContent += `| --- | --- | --- | --- |\n`;
										tool.requestParams.forEach((param: any) => {
											const required = param.requiredFlag ? "是" : "否";
											mdContent += `| ${param.paramName} | ${param.paramType} | ${required} | ${param.paramDesc || ""} |\n`;
										});
										const inputMethod =
											tool.requestParams.find((p: any) => p.inputMethod)
												?.inputMethod || "";
										if (inputMethod) {
											mdContent += `\n- 请求类型： \`${inputMethod}\`\n\n`;
										} else {
											mdContent += "\n";
										}
									}
									if (tool.responseParams && tool.responseParams.length > 0) {
										mdContent += `## 接口出参\n\n`;
										mdContent += `| 字段名 | 类型 | 说明 |\n`;
										mdContent += `| --- | --- | --- |\n`;
										tool.responseParams.forEach((param: any) => {
											mdContent += `| ${param.paramName} | ${param.paramType} | ${param.paramDesc || ""} |\n`;
											if (param.children && param.children.length > 0) {
												collectNestedStructures(
													param.paramName,
													param.children,
												);
											}
										});
									}
									if (nestedStructures.length > 0) {
										nestedStructures.forEach((structure: any) => {
											const levelPrefix = "#".repeat(
												Math.min(3 + structure.level, 6),
											);
											mdContent += `${levelPrefix} ${structure.name} 结构\n\n`;
											mdContent += `| 字段名 | 类型 | 说明 |\n`;
											mdContent += `| --- | --- | --- |\n`;
											structure.children.forEach((child: any) => {
												mdContent += `| ${child.paramName} | ${child.paramType} | ${child.paramDesc || ""} |\n`;
											});
											mdContent += "\n";
										});
									}
								});
							}
						} else {
							mdContent = "# 插件详情\n\n暂无详情信息";
						}
						this.mdProvider.setContent(mdContent);
						const fileName =
							data && data.name ? encodeURIComponent(data.name) : message.id;
						const uri = vscode.Uri.parse(`plugin-detail:${fileName}.md`);
						vscode.commands.executeCommand("markdown.showPreview", uri);
					} catch (err) {
						const mdContent = "# 插件详情\n\n获取详情失败，请稍后重试";
						this.mdProvider.setContent(mdContent);
						const fileName = message.id;
						const uri = vscode.Uri.parse(`plugin-detail:${fileName}.md`);
						vscode.commands.executeCommand("markdown.showPreview", uri);
					}
					break;
				}
				case "openSettings":
					vscode.commands.executeCommand("aiResources.openSettings");
					break;
				case "search":
					this.fetchListData(
						message.query,
						message.pageNum,
						message.pageSize,
					).then((records) => {
						webviewView.webview.postMessage({ type: "listData", records });
					});
					break;
				case "getList":
					this.fetchListData(
						message.query,
						message.pageNum,
						message.pageSize,
					).then((records) => {
						webviewView.webview.postMessage({ type: "listData", records });
					});
					break;
				case "cite": {
					try {
						const res = await getPluginInfo(message.id);
						const data = res?.data?.data;
						let mdContent = "";
						let serviceName = "plugin-detail";
						if (data) {
							serviceName = data.name || message.id;
							mdContent = `# ${data.name} 服务接口API文档\n\n`;
							if (data.tools && data.tools.length > 0) {
								data.tools.forEach((tool: any, index: number) => {
									const nestedStructures: any[] = [];
									const collectNestedStructures = (
										paramName: string,
										children: any[],
										level: number = 0,
									) => {
										nestedStructures.push({
											name: paramName,
											children: children,
											level: level,
										});
										children.forEach((child: any) => {
											if (child.children && child.children.length > 0) {
												collectNestedStructures(
													child.paramName,
													child.children,
													level + 1,
												);
											}
										});
									};
									mdContent += `## 服务名称\n\n${tool.name}\n\n`;
									mdContent += `## 服务描述\n\n${tool.toolDesc || ""}\n\n`;
									mdContent += `## 接口地址\n\n\`${tool.requestMethod.toUpperCase()} ${tool.toolUrl}\`\n\n`;
									if (tool.requestParams && tool.requestParams.length > 0) {
										mdContent += `## 接口入参\n\n`;
										mdContent += `| 参数名 | 类型 | 必填 | 说明 |\n`;
										mdContent += `| --- | --- | --- | --- |\n`;
										tool.requestParams.forEach((param: any) => {
											const required = param.requiredFlag ? "是" : "否";
											mdContent += `| ${param.paramName} | ${param.paramType} | ${required} | ${param.paramDesc || ""} |\n`;
										});
										const inputMethod =
											tool.requestParams.find((p: any) => p.inputMethod)
												?.inputMethod || "";
										if (inputMethod) {
											mdContent += `\n- 请求类型： \`${inputMethod}\`\n\n`;
										} else {
											mdContent += "\n";
										}
									}
									if (tool.responseParams && tool.responseParams.length > 0) {
										mdContent += `## 接口出参\n\n`;
										mdContent += `| 字段名 | 类型 | 说明 |\n`;
										mdContent += `| --- | --- | --- |\n`;
										tool.responseParams.forEach((param: any) => {
											mdContent += `| ${param.paramName} | ${param.paramType} | ${param.paramDesc || ""} |\n`;
											if (param.children && param.children.length > 0) {
												collectNestedStructures(
													param.paramName,
													param.children,
												);
											}
										});
									}
									if (nestedStructures.length > 0) {
										nestedStructures.forEach((structure: any) => {
											const levelPrefix = "#".repeat(
												Math.min(3 + structure.level, 6),
											);
											mdContent += `${levelPrefix} ${structure.name} 结构\n\n`;
											mdContent += `| 字段名 | 类型 | 说明 |\n`;
											mdContent += `| --- | --- | --- |\n`;
											structure.children.forEach((child: any) => {
												mdContent += `| ${child.paramName} | ${child.paramType} | ${child.paramDesc || ""} |\n`;
											});
											mdContent += "\n";
										});
									}
								});
							}
						} else {
							mdContent = "# 插件详情\n\n暂无详情信息";
						}
						// 追加API key说明
						mdContent +=
							"\n## api key说明\n需要使用api key时，从.joycode/ai-resource/apikey.json文件中获取\n";
						try {
							const fileUri = await JoycodeHelper.saveMarkdownToAiResourceDir(
								`${data.name || message.id}.md`,
								mdContent,
							);
							Logger.info(
								`已保存到：${fileUri?.fsPath || fileUri?.toString()}`,
							);
							vscode.window.showInformationMessage(
								`保存成功！路径：${fileUri?.fsPath || fileUri?.toString()}`,
							);
						} catch (e) {
							const msg = e instanceof Error ? e.message : String(e);
							vscode.window.showErrorMessage(`保存失败：${msg}`);
						}
					} catch (err) {
						Logger.error(
							`获取详情失败: ${err instanceof Error ? err.message : String(err)}`,
						);
						const msg = err instanceof Error ? err.message : String(err);
						vscode.window.showErrorMessage(`保存失败：${msg}`);
					}
					break;
				}
			}
		});
	}

	private async getWebviewContent(): Promise<string> {
		const currentKey = getCurrentApiKey();
		const isJoyCode = vscode.env.uriScheme === "joycoder";
		const isLoggedIn = isJoyCode ? await this.isLoggedIn() : false;
		const isRemote = !!vscode.env.remoteName;

		Logger.info(
			`[AIResourcesViewProvider] getWebviewContent - 远程环境: ${isRemote}, JoyCode环境: ${isJoyCode}, API key存在: ${!!currentKey}, 登录状态: ${isLoggedIn}`,
		);

		// 如果不是JoyCode环境，显示错误页面
		if (!isJoyCode) {
			Logger.info("[AIResourcesViewProvider] 显示非JoyCode环境错误页面");
			return `
			<!DOCTYPE html>
			<html lang="zh-CN">
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>环境错误</title>
				<style>
					body { display: flex; align-items: center; justify-content: center; height: 100vh; background: #18181B; color: #fff; margin: 0; }
					.error-container { text-align: center; }
					.error-message { font-size: 16px; color: #ff6b6b; margin-bottom: 16px; }
					.error-description { font-size: 14px; color: #888; }
				</style>
			</head>
			<body>
				<div class="error-container">
					<div class="error-message">⚠️ 环境错误</div>
					<div class="error-description">请在 JoyCoder 软件中使用该插件</div>
				</div>
			</body>
			</html>
			`;
		}

		if (!isLoggedIn) {
			Logger.info("[AIResourcesViewProvider] 显示登录页面");
			// 未登录时显示登录页面
			return `
			<!DOCTYPE html>
			<html lang="zh-CN">
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<title>登录 JoyCoder</title>
				<style>
					body { display: flex; align-items: center; justify-content: center; height: 100vh; background: #18181B; color: #fff; margin: 0; }
					.login-container { text-align: center; }
					.login-btn { padding: 12px 32px; font-size: 16px; background: #2979ff; color: #fff; border: none; border-radius: 4px; cursor: pointer; }
					.login-btn:hover { background: #1565c0; }
				</style>
			</head>
			<body>
				<div class="login-container">
					<!--					<h2>请登录 JoyCoder 账号</h2>-->
<!--					<button class="login-btn" id="loginBtn">登录</button>-->
							<div>For a better experience, please <span style="color: #1565c0;cursor:pointer;" id="loginBtn">sign in</span> to your account.</div>
				</div>
				<script>
					const vscode = acquireVsCodeApi();
					document.getElementById('loginBtn').addEventListener('click', () => {
						vscode.postMessage({ type: 'login' });
					});
				</script>
			</body>
			</html>
			`;
		}
		Logger.info("[AIResourcesViewProvider] 显示资源列表页面");
		const cssUri = this.webviewView?.webview.asWebviewUri(
			vscode.Uri.joinPath(
				this.context.extensionUri,
				"media",
				"iconfont",
				"iconfont.css",
			),
		);
		return `
			<!DOCTYPE html>
			<html>
			<head>
				<meta charset="UTF-8">
				<meta name="viewport" content="width=device-width, initial-scale=1.0">
				<link rel="stylesheet" href="${cssUri}">
				<style>
					html, body { height: 100%; margin: 0; padding: 0; box-sizing: border-box; }
					:root, body {
						--vscode-editor-background: #18181B;
						--vscode-editor-foreground: #d4d4d4;
						--vscode-input-background: #3c3c3c;
						--vscode-input-foreground: #C0C4CC;
						--vscode-input-border: #303035;
						--vscode-button-background: #2979ff;
						--vscode-button-foreground: #ffffff;
						--vscode-button-secondaryBackground: #3a3d41;
						--vscode-button-secondaryForeground: #ffffff;
						--vscode-list-hoverBackground: #202023;
						--vscode-list-activeSelectionBackground: #37373d;
						--vscode-dropdown-background: #3c3c3c;
						--vscode-dropdown-foreground: #f0f0f0;
						--vscode-dropdown-border: #3c3c3c;
						--vscode-checkbox-background: #3c3c3c;
						--vscode-checkbox-foreground: #f0f0f0;
						--vscode-checkbox-border: #3c3c3c;
						--vscode-focusBorder: rgba(255, 255, 255, 0.4) !important;
						--vscode-descriptionForeground: rgba(255, 255, 255, 0.5);
						--vscode-input-label: rgba(255, 255, 255, 0.8);
						--vscode-input-label-placeholder: rgba(255, 255, 255, 0.2);
						--vscode-button-primary2: #F3FBFB;
					}
					body {
						height: 100vh;
						display: flex;
						flex-direction: column;
						background: var(--vscode-editor-background);
						color: var(--vscode-input-label);
						font-family: var(--vscode-font-family);
						margin: 0;
					}
					.container { display: flex; flex-direction: column; height: 100vh; }
					.search-container { position: sticky; top: 0; z-index: 2; background: var(--vscode-editor-background); border-bottom: 1px solid var(--vscode-sideBar-border); margin: 8px 8px 6px; display: flex; align-items: center; gap: 6px; }
					.resource-list { flex: 1 1 0; overflow-y: auto; min-height: 0; padding: 0 8px 8px; }
					.resource-item { padding: 12px; border-radius: 6px; cursor: pointer; position: relative; display: flex; gap: 8px; }
					.resource-item:hover { background: var(--vscode-list-hoverBackground); }
					.resource-logo { width: 40px; height: 40px; flex-shrink: 0; border-radius: 4px; }
					.resource-content { flex: 1; min-width: 0; display: flex; flex-direction: column; }
					.resource-label { font-size: 14px; font-weight: 500; color: var(--vscode-input-label); line-height: 22px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
					.resource-description { font-size: 12px; color: var(--vscode-descriptionForeground); line-height: 18px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
					.cite-tag { display: none; height: 22px; margin-top: 10px; padding: 0 8px; background: var(--vscode-button-primary2); color: var(--vscode-editor-background); border-radius: 4px; font-size: 12px; font-weight: 500; line-height: 22px; }
					.resource-item:hover .cite-tag{ display: inline-block; }
					.search-container .iconfont{ position: absolute; top: 7px; left: 12px; color: var(--vscode-input-label-placeholder); font-size: 16px; }
					.search-input { width: 100%; padding: 6px 12px 6px 32px; border: 1px solid var(--vscode-input-label-placeholder); background: var(--vscode-editor-background); color: var(--vscode-input-label); outline: none; border-radius: 4px; box-sizing: border-box; font-size: 12px; line-height: 18px; }
					.search-input::placeholder { color: var(--vscode-input-label-placeholder); }
					.search-input:focus { border-color: var(--vscode-input-label); }
				</style>
			</head>
			<body>
				<div class="container">
					<div class="search-container">
						<i class="iconfont icon-sousuo"></i>
						<input type="text" class="search-input" placeholder="Search">
					</div>
					<div class="resource-list" id="resourceList"></div>
				</div>
				<script>
				(function() {
					const vscode = acquireVsCodeApi();
					const resourceList = document.getElementById('resourceList');
					const searchInput = document.querySelector('.search-input');
					let currentPage = 1;
					let pageSize = 25;
					let loading = false;
					let finished = false;
					let query = '';
					let allRecords = [];
					let scrollDebounceTimer = null;
					function setLoading() {
						resourceList.innerHTML = '<div style="padding:24px;text-align:center;color:#888;">加载中...</div>';
					}
					function setNoData() {
						resourceList.innerHTML = '<div style="padding:24px;text-align:center;color:#888;">暂无数据</div>';
					}
					function setBottomLoading() {
						let loadingDiv = document.getElementById('bottom-loading');
						if (!loadingDiv) {
							loadingDiv = document.createElement('div');
							loadingDiv.id = 'bottom-loading';
							loadingDiv.style.cssText = 'padding:16px;text-align:center;color:#888;';
							resourceList.appendChild(loadingDiv);
						}
						loadingDiv.innerHTML = '<span class="loading-spinner"></span> 加载中...';
					}
					function removeBottomLoading() {
						const loadingDiv = document.getElementById('bottom-loading');
						if (loadingDiv) loadingDiv.remove();
					}
					function setNoMore() {
						let noMoreDiv = document.getElementById('bottom-nomore');
						if (!noMoreDiv) {
							noMoreDiv = document.createElement('div');
							noMoreDiv.id = 'bottom-nomore';
							noMoreDiv.style.cssText = 'padding:16px;text-align:center;color:#888;';
							resourceList.appendChild(noMoreDiv);
						}
						noMoreDiv.innerText = '没有更多了';
					}
					function removeNoMore() {
						const noMoreDiv = document.getElementById('bottom-nomore');
						if (noMoreDiv) noMoreDiv.remove();
					}
					function renderResources(items, append = false) {
						if (!items || items.length === 0) {
							setNoData();
							return;
						}
						// 虚拟滚动参数
						const VISIBLE_COUNT = 20;
						const itemHeight = 64; // 估算每个item高度
						if (typeof window._virtualStartIndex === 'undefined') window._virtualStartIndex = 0;
						let startIndex = window._virtualStartIndex;
						if (startIndex > items.length - VISIBLE_COUNT) startIndex = Math.max(0, items.length - VISIBLE_COUNT);
						window._virtualStartIndex = startIndex;
						const visibleItems = items.slice(startIndex, startIndex + VISIBLE_COUNT);
						const topPadding = startIndex * itemHeight;
						const bottomPadding = Math.max(0, (items.length - startIndex - VISIBLE_COUNT) * itemHeight);

						let html = '<div style="height:' + topPadding + 'px"></div>';
						html += visibleItems.map(function(item) {
							return (
								'<div class="resource-item" data-label="' + item.label + '" data-id="' + item.id + '">' +
									'<img class="resource-logo" src="' + item.logo + '" alt="' + item.label + '">' +
									'<div class="resource-content">' +
										'<div class="resource-label">' + item.label + '</div>' +
										'<div class="resource-description">' + item.description + '</div>' +
									'</div>' +
									'<span class="cite-tag">引用</span>' +
								'</div>'
							);
						}).join('');
						html += '<div style="height:' + bottomPadding + 'px"></div>';
						resourceList.innerHTML = html;

						document.querySelectorAll('.resource-item').forEach(item => {
							item.addEventListener('click', () => {
								vscode.postMessage({
									type: 'select',
									id: item.dataset.id
								});
							});
							// 给"引用"按钮单独绑定事件，并阻止冒泡
							const citeBtn = item.querySelector('.cite-tag');
							if (citeBtn) {
								citeBtn.addEventListener('click', (e) => {
									e.stopPropagation();
									vscode.postMessage({
										type: 'cite',
										id: item.dataset.id
									});
								});
							}
						});
						// 只在数据全部加载完且最后一条在可见区时显示"没有更多了"
						removeNoMore();
						if (typeof finished !== 'undefined' && finished && (startIndex + VISIBLE_COUNT >= items.length)) {
							setNoMore();
						}
					}
					let debounceTimer = null;
					searchInput.addEventListener('input', (e) => {
						query = e.target.value;
						if (debounceTimer) clearTimeout(debounceTimer);
						setLoading();
						debounceTimer = setTimeout(() => {
							currentPage = 1;
							finished = false;
							allRecords = [];
							vscode.postMessage({ type: 'search', query, pageNum: currentPage, pageSize });
						}, 500);
					});
					setLoading();
					vscode.postMessage({ type: 'getList', pageNum: currentPage, pageSize });
					window.addEventListener('message', event => {
						const msg = event.data;
						if (msg.type === 'listData') {
							const newRecords = msg.records.map(item => ({
								label: item.name,
								description: item.pluginDesc,
								id: item.pluginId,
								logo: item.iconUrl || 'https://files.codelife.cc/website/github.svg',
								icon: 'icon-search',
							}));
							if (currentPage === 1) {
								allRecords = newRecords;
								window._virtualStartIndex = 0;
								renderResources(allRecords);
							} else {
								allRecords = allRecords.concat(newRecords);
								renderResources(allRecords);
							}
							loading = false;
							removeBottomLoading();
							removeNoMore();
							if ((!newRecords.length || (msg.records.length < pageSize)) && allRecords.length > 0) {
								finished = true;
								setNoMore();
							}
						}
					});
					resourceList.addEventListener('scroll', function() {
						const itemHeight = 64;
						const VISIBLE_COUNT = 20;
						const scrollTop = resourceList.scrollTop;
						let newStartIndex = Math.floor(scrollTop / itemHeight);
						// 边界处理
						if (newStartIndex > allRecords.length - VISIBLE_COUNT) newStartIndex = Math.max(0, allRecords.length - VISIBLE_COUNT);
						if (newStartIndex !== window._virtualStartIndex) {
							window._virtualStartIndex = newStartIndex;
							renderResources(allRecords);
						}
						// 懒加载分页逻辑保留
						if (loading || finished) return;
						if (scrollDebounceTimer) clearTimeout(scrollDebounceTimer);
						scrollDebounceTimer = setTimeout(() => {
							if (resourceList.scrollTop + resourceList.clientHeight >= resourceList.scrollHeight - 10) {
								loading = true;
								setBottomLoading();
								currentPage++;
								vscode.postMessage({ type: 'search', query, pageNum: currentPage, pageSize });
							}
						}, 150);
					});
				})();
				</script>
			</body>
			</html>
		`;
	}

	// private getOutFolderName(): string {
	// 	try {
	// 		const pkgPath = path.join(__dirname, "../../package.json");
	// 		const pkg = JSON.parse(fs.readFileSync(pkgPath, "utf8"));
	// 		return pkg.outFloderName || "ai-resource";
	// 	} catch (e) {
	// 		return "ai-resource";
	// 	}
	// }
}




